#!/bin/bash

# Project NOVA Local Setup Script
# This script sets up MCP servers to run locally without Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites for local setup..."
    
    # Check Node.js
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        echo "Visit: https://nodejs.org/"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "All prerequisites are met!"
}

# Function to setup environment
setup_environment() {
    print_status "Setting up local environment..."
    
    # Create local directories
    mkdir -p local-agents/{logs,data,config}
    
    # Setup environment file
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
        fi
    fi
    
    # Create local package.json for managing dependencies
    if [ ! -f local-agents/package.json ]; then
        cat > local-agents/package.json << 'EOF'
{
  "name": "project-nova-local-agents",
  "version": "1.0.0",
  "description": "Local MCP agents for Project NOVA",
  "main": "index.js",
  "scripts": {
    "start:blinko": "mcp-server-blinko",
    "start:paperless": "paperless-mcp",
    "start:karakeep": "@karakeep/mcp",
    "start:siyuan": "@onigeya/siyuan-mcp-server"
  },
  "dependencies": {},
  "devDependencies": {}
}
EOF
        print_success "Created local package.json"
    fi
}

# Function to install MCP servers
install_mcp_servers() {
    print_status "Installing MCP servers locally..."
    
    cd local-agents
    
    # Install supergateway for SSE transport
    print_status "Installing supergateway..."
    npm install -g supergateway
    
    # Install available MCP servers
    print_status "Installing Blinko MCP server..."
    npm install -g mcp-server-blinko@0.0.6 || print_warning "Failed to install Blinko MCP"
    
    print_status "Installing Paperless MCP server..."
    npm install -g @nloui/paperless-mcp || print_warning "Failed to install Paperless MCP"
    
    print_status "Installing Karakeep MCP server..."
    npm install -g @karakeep/mcp || print_warning "Failed to install Karakeep MCP"
    
    print_status "Installing SiYuan MCP server..."
    npm install -g @onigeya/siyuan-mcp-server || print_warning "Failed to install SiYuan MCP"
    
    # Clone and build other MCP servers that need to be built from source
    print_status "Setting up BookStack MCP server..."
    if [ ! -d "bookstack-mcp" ]; then
        git clone https://github.com/yellowgg2/mcp-bookstack.git bookstack-mcp
        cd bookstack-mcp
        npm install --ignore-scripts
        npm run build || print_warning "Failed to build BookStack MCP"
        cd ..
    fi
    
    print_status "Setting up TriliumNext MCP server..."
    if [ ! -d "triliumnext-mcp" ]; then
        git clone https://github.com/tan-yong-sheng/triliumnext-mcp.git triliumnext-mcp
        cd triliumnext-mcp
        npm install
        npm run build || print_warning "Failed to build TriliumNext MCP"
        cd ..
    fi
    
    print_status "Setting up Home Assistant MCP server..."
    if [ ! -d "hass-mcp" ]; then
        git clone https://github.com/voska/hass-mcp.git hass-mcp
        cd hass-mcp
        # This is a Python package, so we'll need pip
        if command -v pip3 >/dev/null 2>&1; then
            pip3 install -r requirements.txt || print_warning "Failed to install Home Assistant MCP dependencies"
        else
            print_warning "Python pip3 not found, skipping Home Assistant MCP"
        fi
        cd ..
    fi
    
    cd ..
    print_success "MCP servers installation completed!"
}

# Function to create startup scripts
create_startup_scripts() {
    print_status "Creating startup scripts for local agents..."
    
    mkdir -p local-agents/scripts
    
    # Create Blinko agent startup script
    cat > local-agents/scripts/start-blinko.sh << 'EOF'
#!/bin/bash
source ../.env
echo "Starting Blinko MCP Agent on port ${BLINKO_PORT:-3001}..."
supergateway --stdio "npx -y mcp-server-blinko@0.0.6" --port ${BLINKO_PORT:-3001} --host ${HOST_IP:-0.0.0.0}
EOF
    
    # Create BookStack agent startup script
    cat > local-agents/scripts/start-bookstack.sh << 'EOF'
#!/bin/bash
source ../.env
echo "Starting BookStack MCP Agent on port ${BOOKSTACK_PORT:-3003}..."
cd ../bookstack-mcp
supergateway --stdio "node build/app.js" --port ${BOOKSTACK_PORT:-3003} --host ${HOST_IP:-0.0.0.0}
EOF
    
    # Create TriliumNext agent startup script
    cat > local-agents/scripts/start-triliumnext.sh << 'EOF'
#!/bin/bash
source ../.env
echo "Starting TriliumNext MCP Agent on port ${TRILIUM_PORT:-3005}..."
cd ../triliumnext-mcp
supergateway --stdio "node dist/index.js" --port ${TRILIUM_PORT:-3005} --host ${HOST_IP:-0.0.0.0}
EOF
    
    # Create SiYuan agent startup script
    cat > local-agents/scripts/start-siyuan.sh << 'EOF'
#!/bin/bash
source ../.env
echo "Starting SiYuan MCP Agent on port ${SIYUAN_PORT:-3006}..."
supergateway --stdio "npx @onigeya/siyuan-mcp-server" --port ${SIYUAN_PORT:-3006} --host ${HOST_IP:-0.0.0.0}
EOF
    
    # Create Paperless agent startup script
    cat > local-agents/scripts/start-paperless.sh << 'EOF'
#!/bin/bash
source ../.env
echo "Starting Paperless MCP Agent on port ${PAPERLESS_PORT:-3008}..."
supergateway --stdio "npx @nloui/paperless-mcp" --port ${PAPERLESS_PORT:-3008} --host ${HOST_IP:-0.0.0.0}
EOF
    
    # Create Karakeep agent startup script
    cat > local-agents/scripts/start-karakeep.sh << 'EOF'
#!/bin/bash
source ../.env
echo "Starting Karakeep MCP Agent on port ${KARAKEEP_PORT:-3009}..."
supergateway --stdio "npx @karakeep/mcp" --port ${KARAKEEP_PORT:-3009} --host ${HOST_IP:-0.0.0.0}
EOF
    
    # Make scripts executable
    chmod +x local-agents/scripts/*.sh
    
    print_success "Startup scripts created!"
}

# Function to create a master startup script
create_master_script() {
    print_status "Creating master startup script..."
    
    cat > start-local-agents.sh << 'EOF'
#!/bin/bash

# Project NOVA Local Agents Startup Script

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Function to start an agent
start_agent() {
    local agent_name=$1
    local script_path=$2
    
    print_status "Starting $agent_name..."
    cd local-agents/scripts
    nohup ./$script_path > ../logs/$agent_name.log 2>&1 &
    echo $! > ../logs/$agent_name.pid
    cd ../..
    print_success "$agent_name started (PID: $(cat local-agents/logs/$agent_name.pid))"
}

# Function to show help
show_help() {
    echo "Project NOVA Local Agents Manager"
    echo ""
    echo "Usage: $0 [OPTION] [AGENT]"
    echo ""
    echo "Options:"
    echo "  start [agent]  - Start specific agent or all agents"
    echo "  stop [agent]   - Stop specific agent or all agents"
    echo "  status         - Show status of all agents"
    echo "  logs [agent]   - Show logs for specific agent"
    echo "  help           - Show this help message"
    echo ""
    echo "Available agents:"
    echo "  blinko, bookstack, triliumnext, siyuan, paperless, karakeep, all"
}

# Create logs directory
mkdir -p local-agents/logs

case "${1:-help}" in
    "start")
        case "${2:-all}" in
            "all")
                print_status "🚀 Starting all local MCP agents..."
                start_agent "blinko" "start-blinko.sh"
                start_agent "bookstack" "start-bookstack.sh"
                start_agent "triliumnext" "start-triliumnext.sh"
                start_agent "siyuan" "start-siyuan.sh"
                start_agent "paperless" "start-paperless.sh"
                start_agent "karakeep" "start-karakeep.sh"
                echo ""
                print_success "All agents started! Check status with: $0 status"
                ;;
            "blinko"|"bookstack"|"triliumnext"|"siyuan"|"paperless"|"karakeep")
                start_agent "$2" "start-$2.sh"
                ;;
            *)
                echo "Unknown agent: $2"
                show_help
                ;;
        esac
        ;;
    "stop")
        print_status "Stopping agents..."
        for pidfile in local-agents/logs/*.pid; do
            if [ -f "$pidfile" ]; then
                pid=$(cat "$pidfile")
                agent=$(basename "$pidfile" .pid)
                if kill "$pid" 2>/dev/null; then
                    print_success "Stopped $agent (PID: $pid)"
                    rm "$pidfile"
                else
                    print_status "$agent was not running"
                fi
            fi
        done
        ;;
    "status")
        print_status "Agent Status:"
        echo ""
        for pidfile in local-agents/logs/*.pid; do
            if [ -f "$pidfile" ]; then
                pid=$(cat "$pidfile")
                agent=$(basename "$pidfile" .pid)
                if kill -0 "$pid" 2>/dev/null; then
                    echo -e "  ${GREEN}✓${NC} $agent (PID: $pid) - Running"
                else
                    echo -e "  ${RED}✗${NC} $agent - Stopped"
                    rm "$pidfile"
                fi
            fi
        done
        ;;
    "logs")
        if [ -n "$2" ]; then
            tail -f "local-agents/logs/$2.log"
        else
            echo "Please specify an agent name"
            show_help
        fi
        ;;
    "help"|*)
        show_help
        ;;
esac
EOF
    
    chmod +x start-local-agents.sh
    print_success "Master startup script created!"
}

# Function to show completion message
show_completion() {
    echo ""
    print_success "🎉 Project NOVA Local Setup Complete!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Edit .env file with your API keys and service URLs"
    echo "2. Start n8n separately (or use Docker): docker run -it --rm --name n8n -p 5678:5678 n8nio/n8n"
    echo "3. Start local agents: ./start-local-agents.sh start"
    echo "4. Check agent status: ./start-local-agents.sh status"
    echo ""
    echo "🌐 Local Agent URLs (after starting):"
    echo "   📝 Blinko Agent:         http://localhost:3001"
    echo "   📚 BookStack Agent:      http://localhost:3003"
    echo "   🗂️  TriliumNext Agent:    http://localhost:3005"
    echo "   📋 SiYuan Agent:         http://localhost:3006"
    echo "   📄 Paperless Agent:      http://localhost:3008"
    echo "   🔖 Karakeep Agent:       http://localhost:3009"
    echo ""
    echo "📋 Useful commands:"
    echo "   Start all agents:    ./start-local-agents.sh start"
    echo "   Stop all agents:     ./start-local-agents.sh stop"
    echo "   Check status:        ./start-local-agents.sh status"
    echo "   View logs:           ./start-local-agents.sh logs [agent-name]"
}

# Main script logic
case "${1:-setup}" in
    "setup")
        print_status "🚀 Setting up Project NOVA for local execution..."
        check_prerequisites
        setup_environment
        install_mcp_servers
        create_startup_scripts
        create_master_script
        show_completion
        ;;
    "help"|"-h"|"--help")
        echo "Project NOVA Local Setup Script"
        echo ""
        echo "Usage: $0 [OPTION]"
        echo ""
        echo "Options:"
        echo "  setup     - Full local setup (default)"
        echo "  help      - Show this help message"
        ;;
    *)
        print_error "Unknown option: $1"
        exit 1
        ;;
esac
