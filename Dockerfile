# Project NOVA - Multi-Agent AI Architecture
# This Dockerfile creates a lightweight container for managing the Project NOVA ecosystem

FROM node:20-alpine

# Install required system dependencies
RUN apk add --no-cache \
    git \
    curl \
    bash \
    docker-cli \
    docker-compose

# Set working directory
WORKDIR /app

# Copy project files
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/config

# Install any Node.js dependencies if package.json exists
# (Currently this project doesn't have a package.json, but this prepares for future use)
RUN if [ -f package.json ]; then npm install; fi

# Create a startup script
RUN cat > /app/start.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 Starting Project NOVA Multi-Agent Architecture..."
echo "=================================================="

# Check if docker-compose.yml exists
if [ ! -f docker-compose.yml ]; then
    echo "❌ Error: docker-compose.yml not found!"
    exit 1
fi

# Check if .env file exists, create a template if not
if [ ! -f .env ]; then
    echo "📝 Creating .env template file..."
    cat > .env << 'ENVEOF'
# Project NOVA Environment Configuration
# Copy this file and update the values according to your setup

# n8n Configuration
N8N_USER=admin
N8N_PASSWORD=password
N8N_HOST=localhost
TIMEZONE=UTC

# Blinko Agent
BLINKO_DOMAIN=http://localhost:3001
BLINKO_API_KEY=your-blinko-api-key

# BookStack Agent
BOOKSTACK_API_URL=http://localhost
BOOKSTACK_API_TOKEN=your-bookstack-token

# Home Assistant Agent
HA_URL=http://localhost:8123
HA_TOKEN=your-home-assistant-token

# OBS Agent
OBS_HOST=localhost
OBS_PORT=4455
OBS_PASSWORD=your-obs-password

# Reaper Agent
REAPER_HOST=localhost
REAPER_SEND_PORT=8000
REAPER_RECEIVE_PORT=9000
DEBUG_MODE=false

# Langfuse Agent
LANGFUSE_SECRET_KEY=your-langfuse-secret-key
LANGFUSE_PUBLIC_KEY=your-langfuse-public-key
LANGFUSE_HOST=https://cloud.langfuse.com

# Outline Agent
OUTLINE_API_KEY=your-outline-api-key
OUTLINE_API_URL=https://your-outline.com

# Puppeteer Agent
ALLOW_DANGEROUS=false
ENVEOF
    echo "✅ Created .env template. Please update it with your configuration."
fi

echo "🔧 Project NOVA is ready!"
echo "📖 Please refer to the README.md for setup instructions."
echo "🌐 Main services will be available at:"
echo "   - n8n Dashboard: http://localhost:5678"
echo "   - Router Agent: http://localhost:3000"
echo "   - Blinko Agent: http://localhost:3001"
echo "   - BookStack Agent: http://localhost:3003"
echo "   - Home Assistant Agent: http://localhost:3004"
echo "   - Puppeteer Agent: http://localhost:3007"
echo "   - OBS Agent: http://localhost:3012"
echo "   - Reaper Agent: http://localhost:3013"
echo "   - Langfuse Agent: http://localhost:3014"
echo "   - Outline Agent: http://localhost:6060"
echo ""
echo "To start all services, run: docker-compose up -d"
echo "To view logs, run: docker-compose logs -f"
echo "To stop all services, run: docker-compose down"

# Keep container running
tail -f /dev/null
EOF

# Make the startup script executable
RUN chmod +x /app/start.sh

# Expose the main application port (n8n)
EXPOSE 5678

# Set the default command
CMD ["/app/start.sh"]
