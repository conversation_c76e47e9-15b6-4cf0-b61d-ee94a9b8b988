#!/bin/bash

# Project NOVA Quick Start Script
# This script starts only the core working services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to setup environment
setup_env() {
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
        fi
    fi
}

# Function to start core services
start_core_services() {
    print_status "Starting Project NOVA core services..."
    
    # Start n8n first
    print_status "Starting n8n workflow server..."
    docker-compose up -d n8n
    
    # Wait a bit for n8n to start
    sleep 5
    
    # Try to start working agents one by one
    working_agents=("blinko-agent" "bookstack-agent" "home-assistant-agent" "puppeteer-agent" "obs-agent" "langfuse-agent")
    
    for agent in "${working_agents[@]}"; do
        print_status "Attempting to start $agent..."
        if docker-compose up -d "$agent" 2>/dev/null; then
            print_success "$agent started successfully"
        else
            print_warning "$agent failed to start, skipping..."
        fi
        sleep 2
    done
}

# Function to show status
show_status() {
    print_status "Current service status:"
    docker-compose ps
    
    echo ""
    print_success "🚀 Project NOVA Core Services Status"
    echo ""
    echo "📊 Available Services:"
    echo "   🔧 n8n Dashboard:        http://localhost:5678"
    echo "      Username: admin"
    echo "      Password: password"
    echo ""
    
    # Check which services are actually running
    if docker-compose ps | grep -q "project-nova-blinko.*Up"; then
        echo "   📝 Blinko Agent:         http://localhost:3001"
    fi
    if docker-compose ps | grep -q "project-nova-bookstack.*Up"; then
        echo "   📚 BookStack Agent:      http://localhost:3003"
    fi
    if docker-compose ps | grep -q "project-nova-homeassistant.*Up"; then
        echo "   🏠 Home Assistant Agent: http://localhost:3004"
    fi
    if docker-compose ps | grep -q "project-nova-puppeteer.*Up"; then
        echo "   🌐 Puppeteer Agent:      http://localhost:3007"
    fi
    if docker-compose ps | grep -q "project-nova-obs.*Up"; then
        echo "   📹 OBS Agent:            http://localhost:3012"
    fi
    if docker-compose ps | grep -q "project-nova-langfuse.*Up"; then
        echo "   📈 Langfuse Agent:       http://localhost:3014"
    fi
    
    echo ""
    echo "📋 Useful commands:"
    echo "   View logs:     docker-compose logs -f"
    echo "   Stop services: docker-compose down"
    echo "   Restart:       docker-compose restart"
}

# Function to show help
show_help() {
    echo "Project NOVA Quick Start Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  start     - Start core services (default)"
    echo "  stop      - Stop all services"
    echo "  status    - Show service status"
    echo "  logs      - Show service logs"
    echo "  help      - Show this help message"
    echo ""
}

# Main script logic
case "${1:-start}" in
    "start")
        print_status "🚀 Starting Project NOVA core services..."
        check_docker
        setup_env
        start_core_services
        echo ""
        show_status
        ;;
    "stop")
        print_status "Stopping Project NOVA services..."
        docker-compose down
        print_success "Services stopped!"
        ;;
    "status")
        show_status
        ;;
    "logs")
        print_status "Showing Project NOVA logs (Ctrl+C to exit):"
        docker-compose logs -f
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
