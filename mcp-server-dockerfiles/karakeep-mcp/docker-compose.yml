services:
  karakeep-mcp:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: karakeep-mcp
    environment:
      - KARAKEEP_API_ADDR=<YOUR_KARAKEEP_API_URL>
      - KARAKEEP_API_KEY=<YOUR_KARAKEEP_API_KEY>
      - SSE_PORT=<YOUR_SSE_PORT>
      - SSE_HOST=0.0.0.0
      - N8N_HOST=<YOUR_N8N_IP>
      - N8N_PORT=<YOUR_N8N_PORT>
    ports:
      - "<YOUR_SSE_PORT>:<YOUR_SSE_PORT>"
    restart: unless-stopped