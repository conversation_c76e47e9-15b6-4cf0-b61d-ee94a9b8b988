services:
  ragflow-mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ragflow-mcp-server
    restart: unless-stopped
    ports:
      - "${MCP_SSE_PORT}:${MCP_SSE_PORT}"
    environment:
      - RAGFLOW_URL=${RAGFLOW_URL}
      - RAGFLOW_API_KEY=${RAGFLOW_API_KEY}
      - RAGFLOW_DATASET_ID=${RAGFLOW_DATASET_ID}
      - RAGFLOW_SIMILARITY_THRESHOLD=${RAGFLOW_SIMILARITY_THRESHOLD}
      - RAGFLOW_VECTOR_SIMILARITY_WEIGHT=${RAGFLOW_VECTOR_SIMILARITY_WEIGHT}
      - RAGFLOW_LIMIT=${RAGFLOW_LIMIT}
      - MCP_SSE_PORT=${MCP_SSE_PORT}
    volumes:
      - ./logs:/app/logs
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
