services:
  mcp-bookstack-sse:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mcp-bookstack-sse
    restart: unless-stopped
    ports:
      - "${PORT:-3003}:${PORT:-3003}"
    environment:
      - HOST=${HOST:-0.0.0.0}
      - PORT=${PORT:-3003}
      - BOOKSTACK_API_URL=${BOOKSTACK_API_URL}
      - BOOKSTACK_API_TOKEN=${BOOKSTACK_API_TOKEN}
      - BOOKSTACK_API_KEY=${BOOKSTACK_API_KEY}
    volumes:
      - mcp-bookstack-data:/app/data
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge

volumes:
  mcp-bookstack-data:
    driver: local
