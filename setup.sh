#!/bin/bash

# Project NOVA Setup Script
# This script helps you set up and run the Project NOVA multi-agent architecture

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        echo "Visit: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    print_success "All prerequisites are met!"
}

# Function to setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
            print_warning "Please edit .env file with your actual configuration values"
        else
            print_error ".env.example file not found!"
            exit 1
        fi
    else
        print_warning ".env file already exists. Skipping creation."
    fi
}

# Function to build Docker images
build_images() {
    print_status "Building Docker images..."
    
    # Build individual MCP server images
    print_status "Building MCP server images..."
    
    # List of MCP servers to build (excluding problematic ones for now)
    mcp_servers=(
        "blinko-mcp"
        "bookstack-mcp"
        "cli-server-mcp"
        "home-assisstant-mcp"
        "puppeteer-mcp"
        "obs-mcp"
        # "reaper-mcp"   # Temporarily disabled due to git clone issues
        "langfuse-mcp"
        # "outline-mcp"  # Temporarily disabled due to build issues
    )
    
    for server in "${mcp_servers[@]}"; do
        if [ -d "mcp-server-dockerfiles/$server" ]; then
            print_status "Building $server..."
            docker build -t "project-nova-$server" "mcp-server-dockerfiles/$server" || {
                print_warning "Failed to build $server, continuing..."
            }
        else
            print_warning "Directory mcp-server-dockerfiles/$server not found, skipping..."
        fi
    done
    
    print_success "Docker images built successfully!"
}

# Function to start services
start_services() {
    print_status "Starting Project NOVA services..."
    
    # Create necessary directories
    mkdir -p data logs config
    
    # Start services with docker-compose
    docker-compose up -d
    
    print_success "Project NOVA services started!"
    print_status "Services are starting up. This may take a few minutes..."
    
    # Wait a bit for services to start
    sleep 10
    
    print_status "Checking service status..."
    docker-compose ps
}

# Function to show service URLs
show_urls() {
    echo ""
    print_success "🚀 Project NOVA is running!"
    echo ""
    echo "📊 Service URLs:"
    echo "   🔧 n8n Dashboard:        http://localhost:5678"
    echo "   🤖 Router Agent:         http://localhost:3000"
    echo "   📝 Blinko Agent:         http://localhost:3001"
    echo "   📚 BookStack Agent:      http://localhost:3003"
    echo "   🏠 Home Assistant Agent: http://localhost:3004"
    echo "   🌐 Puppeteer Agent:      http://localhost:3007"
    echo "   📹 OBS Agent:            http://localhost:3012"
    echo "   🎵 Reaper Agent:         http://localhost:3013"
    echo "   📈 Langfuse Agent:       http://localhost:3014"
    echo "   📖 Outline Agent:        http://localhost:6060"
    echo ""
    echo "🔑 Default n8n credentials:"
    echo "   Username: admin"
    echo "   Password: password"
    echo ""
    echo "📋 Useful commands:"
    echo "   View logs:     docker-compose logs -f"
    echo "   Stop services: docker-compose down"
    echo "   Restart:       docker-compose restart"
    echo "   Update:        docker-compose pull && docker-compose up -d"
}

# Function to show help
show_help() {
    echo "Project NOVA Setup Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  setup     - Full setup (check prerequisites, setup env, build, start)"
    echo "  build     - Build Docker images only"
    echo "  start     - Start services only"
    echo "  stop      - Stop all services"
    echo "  restart   - Restart all services"
    echo "  status    - Show service status"
    echo "  logs      - Show service logs"
    echo "  clean     - Stop and remove all containers and volumes"
    echo "  help      - Show this help message"
    echo ""
}

# Main script logic
case "${1:-setup}" in
    "setup")
        print_status "🚀 Starting Project NOVA setup..."
        check_prerequisites
        setup_environment
        build_images
        start_services
        show_urls
        ;;
    "build")
        check_prerequisites
        build_images
        ;;
    "start")
        check_prerequisites
        start_services
        show_urls
        ;;
    "stop")
        print_status "Stopping Project NOVA services..."
        docker-compose down
        print_success "Services stopped!"
        ;;
    "restart")
        print_status "Restarting Project NOVA services..."
        docker-compose restart
        print_success "Services restarted!"
        ;;
    "status")
        print_status "Project NOVA service status:"
        docker-compose ps
        ;;
    "logs")
        print_status "Showing Project NOVA logs (Ctrl+C to exit):"
        docker-compose logs -f
        ;;
    "clean")
        print_warning "This will stop and remove all containers and volumes!"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v --remove-orphans
            docker system prune -f
            print_success "Cleanup completed!"
        else
            print_status "Cleanup cancelled."
        fi
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
