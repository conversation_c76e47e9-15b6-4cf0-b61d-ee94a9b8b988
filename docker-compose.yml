version: '3.8'

services:
  # n8n workflow automation server
  n8n:
    image: n8nio/n8n:latest
    container_name: project-nova-n8n
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD:-password}
      - N8N_HOST=${N8N_HOST:-localhost}
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=production
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=${TIMEZONE:-UTC}
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n-workflows:/home/<USER>/.n8n/workflows:ro
    networks:
      - nova-network

  # Router Agent - Main orchestration agent
  router-agent:
    build:
      context: ./mcp-server-dockerfiles/cli-server-mcp
      dockerfile: Dockerfile
    container_name: project-nova-router
    restart: unless-stopped
    ports:
      - "3000:3009"
    environment:
      - HOST_IP=0.0.0.0
      - SSE_PORT=3009
      - ALLOWED_DIR=/data
      - ALLOWED_COMMANDS=all
      - ALLOWED_FLAGS=all
      - MAX_COMMAND_LENGTH=4096
      - COMMAND_TIMEOUT=60
      - ALLOW_SHELL_OPERATORS=true
    volumes:
      - router_data:/data
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - nova-network
    depends_on:
      - n8n

  # Blinko Agent - Note-taking and knowledge management
  blinko-agent:
    build:
      context: ./mcp-server-dockerfiles/blinko-mcp
      dockerfile: Dockerfile
    container_name: project-nova-blinko
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - HOST_IP=0.0.0.0
      - SSE_PORT=3001
      - BLINKO_DOMAIN=${BLINKO_DOMAIN:-http://localhost:3001}
      - BLINKO_API_KEY=${BLINKO_API_KEY:-your-api-key}
    volumes:
      - blinko_logs:/app/logs
    networks:
      - nova-network

  # BookStack Agent - Documentation management
  bookstack-agent:
    build:
      context: ./mcp-server-dockerfiles/bookstack-mcp
      dockerfile: Dockerfile
    container_name: project-nova-bookstack
    restart: unless-stopped
    ports:
      - "3003:3003"
    environment:
      - PORT=3003
      - BOOKSTACK_API_URL=${BOOKSTACK_API_URL:-http://localhost}
      - BOOKSTACK_API_TOKEN=${BOOKSTACK_API_TOKEN:-your-token}
    networks:
      - nova-network

  # Home Assistant Agent - Smart home automation
  home-assistant-agent:
    build:
      context: ./mcp-server-dockerfiles/home-assisstant-mcp
      dockerfile: Dockerfile
    container_name: project-nova-homeassistant
    restart: unless-stopped
    ports:
      - "3004:3004"
    environment:
      - HA_URL=${HA_URL:-http://localhost:8123}
      - HA_TOKEN=${HA_TOKEN:-your-ha-token}
      - SSE_PORT=3004
      - HOST_IP=0.0.0.0
    volumes:
      - hass_data:/data
    networks:
      - nova-network

  # Puppeteer Agent - Web automation
  puppeteer-agent:
    build:
      context: ./mcp-server-dockerfiles/puppeteer-mcp
      dockerfile: Dockerfile
    container_name: project-nova-puppeteer
    restart: unless-stopped
    ports:
      - "3007:3007"
    environment:
      - SSE_HOST=0.0.0.0
      - SSE_PORT=3007
      - DOCKER_CONTAINER=true
      - PUPPETEER_LAUNCH_OPTIONS="{\"headless\":true}"
      - ALLOW_DANGEROUS=${ALLOW_DANGEROUS:-false}
    volumes:
      - puppeteer_data:/app/data
    networks:
      - nova-network

  # OBS Agent - Streaming and recording
  obs-agent:
    build:
      context: ./mcp-server-dockerfiles/obs-mcp
      dockerfile: Dockerfile
    container_name: project-nova-obs
    restart: unless-stopped
    ports:
      - "3012:3012"
    environment:
      - SSE_PORT=3012
      - OBS_HOST=${OBS_HOST:-localhost}
      - OBS_PORT=${OBS_PORT:-4455}
      - OBS_PASSWORD=${OBS_PASSWORD:-your-obs-password}
    networks:
      - nova-network

  # Reaper Agent - Digital Audio Workstation (commented out due to build issues)
  # reaper-agent:
  #   build:
  #     context: ./mcp-server-dockerfiles/reaper-mcp
  #     dockerfile: Dockerfile
  #   container_name: project-nova-reaper
  #   restart: unless-stopped
  #   ports:
  #     - "3013:3013"
  #   environment:
  #     - REAPER_HOST=${REAPER_HOST:-localhost}
  #     - REAPER_SEND_PORT=${REAPER_SEND_PORT:-8000}
  #     - REAPER_RECEIVE_PORT=${REAPER_RECEIVE_PORT:-9000}
  #     - SSE_PORT=3013
  #     - DEBUG_MODE=${DEBUG_MODE:-false}
  #   networks:
  #     - nova-network

  # Langfuse Agent - LLM observability
  langfuse-agent:
    build:
      context: ./mcp-server-dockerfiles/langfuse-mcp
      dockerfile: Dockerfile
    container_name: project-nova-langfuse
    restart: unless-stopped
    ports:
      - "3014:3014"
    environment:
      - SSE_PORT=3014
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY:-your-secret-key}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY:-your-public-key}
      - LANGFUSE_HOST=${LANGFUSE_HOST:-https://cloud.langfuse.com}
    networks:
      - nova-network

  # Outline Agent - Team wiki (commented out due to build issues)
  # outline-agent:
  #   build:
  #     context: ./mcp-server-dockerfiles/outline-mcp
  #     dockerfile: Dockerfile
  #   container_name: project-nova-outline
  #   restart: unless-stopped
  #   ports:
  #     - "6060:6060"
  #   environment:
  #     - OUTLINE_API_KEY=${OUTLINE_API_KEY:-your-api-key}
  #     - OUTLINE_API_URL=${OUTLINE_API_URL:-https://your-outline.com}
  #   networks:
  #     - nova-network

networks:
  nova-network:
    driver: bridge

volumes:
  n8n_data:
    driver: local
  router_data:
    driver: local
  blinko_logs:
    driver: local
  hass_data:
    driver: local
  puppeteer_data:
    driver: local
