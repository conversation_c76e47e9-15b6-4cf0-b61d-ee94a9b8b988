# Project NOVA Local Setup Guide

This guide will help you run all Project NOVA agents locally without Dock<PERSON>.

## Prerequisites

- **Node.js 18+**: [Download Node.js](https://nodejs.org/)
- **npm**: Comes with Node.js
- **Python 3.8+**: For some agents (optional)
- **Git**: For cloning repositories

## Quick Local Setup

### 1. Run the Local Setup Script

```bash
# Make the script executable
chmod +x local-setup.sh

# Run the full local setup
./local-setup.sh setup
```

This script will:
- Check prerequisites
- Install MCP servers via npm
- Clone and build source-based agents
- Create startup scripts for each agent
- Set up a master control script

### 2. Configure Environment Variables

```bash
# Copy the environment template
cp .env.example .env

# Edit with your actual API keys and service URLs
nano .env
```

### 3. Start n8n (Choose one option)

#### Option A: Using Docker (Recommended)
```bash
docker run -it --rm --name n8n -p 5678:5678 n8nio/n8n
```

#### Option B: Install n8n locally
```bash
npm install -g n8n
n8n start
```

### 4. Start Local Agents

```bash
# Start all agents
./start-local-agents.sh start

# Or start individual agents
./start-local-agents.sh start blinko
./start-local-agents.sh start bookstack
```

## Available Local Agents

After setup, these agents will be available:

| Agent | Port | URL | Description |
|-------|------|-----|-------------|
| Blinko | 3001 | http://localhost:3001 | Note-taking and knowledge management |
| BookStack | 3003 | http://localhost:3003 | Documentation management |
| TriliumNext | 3005 | http://localhost:3005 | Hierarchical note-taking |
| SiYuan | 3006 | http://localhost:3006 | Knowledge base management |
| Paperless | 3008 | http://localhost:3008 | Document management |
| Karakeep | 3009 | http://localhost:3009 | Bookmark and content management |

## Managing Local Agents

### Start/Stop Agents

```bash
# Start all agents
./start-local-agents.sh start

# Start specific agent
./start-local-agents.sh start blinko

# Stop all agents
./start-local-agents.sh stop

# Check status
./start-local-agents.sh status

# View logs
./start-local-agents.sh logs blinko
```

### Check Agent Status

```bash
./start-local-agents.sh status
```

Example output:
```
Agent Status:

  ✓ blinko (PID: 12345) - Running
  ✓ bookstack (PID: 12346) - Running
  ✗ siyuan - Stopped
```

## Configuration

### Required Environment Variables

Edit your `.env` file with the following:

```bash
# General
HOST_IP=0.0.0.0

# Blinko Agent
BLINKO_DOMAIN=http://your-blinko-instance.com
BLINKO_API_KEY=your-blinko-api-key

# BookStack Agent
BOOKSTACK_API_URL=http://your-bookstack-instance.com
BOOKSTACK_API_TOKEN=your-bookstack-token

# Home Assistant (if using)
HA_URL=http://your-home-assistant:8123
HA_TOKEN=your-home-assistant-token

# SiYuan Agent
SIYUAN_TOKEN=your-siyuan-token
SIYUAN_SERVER=http://localhost:6806

# Paperless Agent
PAPERLESS_URL=http://your-paperless-instance:8000
PAPERLESS_TOKEN=your-paperless-token

# Karakeep Agent
KARAKEEP_API_ADDR=http://your-karakeep-instance:8080
KARAKEEP_API_KEY=your-karakeep-api-key
```

## Connecting to n8n

1. Access n8n at http://localhost:5678
2. Create a new workflow
3. Add an "MCP Client" node
4. Configure the MCP Client to connect to your local agents:
   - **Server URL**: `http://localhost:3001` (for Blinko agent)
   - **Transport**: SSE (Server-Sent Events)

## Troubleshooting

### Agent Won't Start

1. Check if the port is already in use:
   ```bash
   lsof -i :3001
   ```

2. Check the agent logs:
   ```bash
   ./start-local-agents.sh logs blinko
   ```

3. Verify environment variables are set correctly in `.env`

### Permission Errors

Make sure scripts are executable:
```bash
chmod +x local-setup.sh
chmod +x start-local-agents.sh
chmod +x local-agents/scripts/*.sh
```

### Missing Dependencies

If an agent fails to install:
```bash
# Reinstall globally
npm install -g mcp-server-blinko@0.0.6

# Or install locally in the project
cd local-agents
npm install mcp-server-blinko@0.0.6
```

### Python-based Agents

For agents that require Python (like Home Assistant MCP):
```bash
# Install Python dependencies
pip3 install -r local-agents/hass-mcp/requirements.txt
```

## File Structure

After setup, your directory will look like:
```
project-nova/
├── local-agents/
│   ├── scripts/           # Individual agent startup scripts
│   ├── logs/             # Agent log files
│   ├── data/             # Agent data directory
│   ├── bookstack-mcp/    # Cloned BookStack MCP
│   ├── triliumnext-mcp/  # Cloned TriliumNext MCP
│   └── package.json      # Local dependencies
├── start-local-agents.sh  # Master control script
├── local-setup.sh        # Setup script
└── .env                  # Your configuration
```

## Next Steps

1. **Configure your external services** (Blinko, BookStack, etc.) and update `.env`
2. **Import n8n workflows** from the `n8n-workflows/` directory
3. **Test agent connections** by accessing their URLs
4. **Create custom workflows** in n8n to orchestrate your agents

## Support

If you encounter issues:
1. Check the agent logs: `./start-local-agents.sh logs [agent-name]`
2. Verify your `.env` configuration
3. Ensure all required external services are running
4. Check that ports aren't conflicting with other services
