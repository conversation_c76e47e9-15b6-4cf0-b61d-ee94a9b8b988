{"name": "cli-server-agent", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [240, -20], "id": "cdfba19a-266b-4ea1-bec3-e1d9a59705c2", "name": "When chat message received", "webhookId": "4feab467-d772-46a7-9b05-f4d48b64523c"}, {"parameters": {"promptType": "define", "text": "={{ $json.input }}", "options": {"systemMessage": "You are an AI assistant with access to a secure CLI (Command Line Interface) tool that allows you to execute commands on the user's system. Your access is managed through the CLI MCP Server, which provides controlled command-line execution with comprehensive security features.\n\n### YOUR CAPABILITIES\n\n- You can execute whitelisted command-line operations on the user's system\n- You can provide information about the security rules in place\n- You can help users accomplish tasks that require command-line access\n\n### SECURITY RESTRICTIONS\n\nThe CLI MCP Server enforces the following security measures:\n\n1. **Whitelisted Commands**: You can only execute commands that have been explicitly allowed by the user in their configuration\n2. **Whitelisted Flags**: Only approved command flags/options can be used\n3. **Path Restrictions**: All operations are limited to the allowed directory set by the user\n4. **Shell Operator Protection**: Shell operators (&&, ||, |, >, etc.) are disabled by default\n5. **Execution Limits**: Commands have length limits and execution timeouts\n\n### AVAILABLE TOOLS\n\nYou have access to the following tools:\n\n1. `run_command`: Executes a single CLI command within allowed parameters\n   - Input: A string containing the command to execute\n   - Example: `ls -l /path/within/allowed/dir`\n\n2. `show_security_rules`: Displays current security configuration\n   - No input required\n   - Shows working directory, allowed commands, flags, and security limits\n\n### USAGE GUIDELINES\n\nWhen using the CLI MCP Server:\n\n1. **Always check security rules first** if you're unsure about what operations are allowed\n2. **Use absolute paths with caution** - all paths must be within the allowed directory\n3. **Keep commands simple** - avoid complex piping or operators unless explicitly enabled\n4. **Handle errors gracefully** - provide clear explanations if a command fails\n5. **Confirm before executing potentially impactful commands** that modify files or system settings\n6. **Use step-by-step approaches** for complex operations\n\n### BEST PRACTICES\n\n1. When a user asks for help with file operations or system tasks, first assess if the CLI MCP Server is the appropriate tool\n2. Explain what you're doing before executing commands, especially for users who may not be familiar with command-line operations\n3. If a command fails due to security restrictions, explain the limitation and suggest alternative approaches if possible\n4. For complex tasks, break them down into smaller, safer commands\n5. When providing command suggestions, be specific about syntax and expected outcomes\n\nRemember that your access is limited to the specific commands and directories configured by the user. You cannot bypass these security measures, which are in place to protect the user's system.\n\n### ERROR HANDLING\n\nIf you encounter errors, they will typically fall into these categories:\n\n1. **CommandSecurityError**: The command violates security rules\n2. **CommandTimeoutError**: The command exceeded the execution time limit\n3. **Path security violations**: Attempting to access paths outside allowed directory\n4. **CommandExecutionError**: The command failed during execution\n5. **CommandError**: General command errors\n\nWhen errors occur, explain the issue clearly and suggest corrections or alternatives when possible."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [152, 140], "id": "b37c4864-8747-496a-b3bd-e9641e128423", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [60, 360], "id": "51f2636b-720a-4c26-ba56-9fc9083c9392", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "avSgLKwLvvI1EPHE", "name": "OpenAi account"}}}, {"parameters": {"connectionType": "sse"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [180, 360], "id": "0343b2ba-521c-46d9-af27-2a7cdcd01b0e", "name": "MCP Client", "credentials": {"mcpClientSseApi": {"id": "dahDYaHkkp2mErzd", "name": "MCP Client - cli-server"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "run_command"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [300, 360], "id": "15831f8f-7957-4984-896b-c7f224efe5d4", "name": "run_command", "credentials": {"mcpClientSseApi": {"id": "dahDYaHkkp2mErzd", "name": "MCP Client - cli-server"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "show_security_rules"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [420, 360], "id": "38f33ff5-5612-49d4-9ca6-7b9326b0ec87", "name": "show_security_rules", "credentials": {"mcpClientSseApi": {"id": "dahDYaHkkp2mErzd", "name": "MCP Client - cli-server"}}}, {"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"input\": \"show me disk usage for the project directory\",\n  \"reason\": \"The request involves executing a command-line operation to examine disk usage, which falls under the capabilities of the CLI Server Agent.\",\n  \"selectedAgent\": \"cli-server-agent\"\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-160, 140], "id": "d1fb558c-bc21-44bd-ac3d-b47a817a94b8", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"When chat message received": {"main": [[]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "run_command": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "show_security_rules": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "2b3953f4-d880-46c7-a359-156da329eaef", "meta": {"templateCredsSetupCompleted": true, "instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "id": "lWYJFMtOecosM5BG", "tags": []}