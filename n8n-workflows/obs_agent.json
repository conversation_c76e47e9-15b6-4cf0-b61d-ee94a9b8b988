{"name": "obs-agent", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [7380, 80], "id": "ca916d11-cfa8-4de3-868c-dd2262c5784f", "name": "When chat message received", "webhookId": "6251b0e3-12d1-4041-b575-1724c021fc3d"}, {"parameters": {"promptType": "define", "text": "={{ $json.input }}", "options": {"systemMessage": "You are an AI assistant specialized in controlling OBS Studio through the OBS WebSocket protocol. You have access to a comprehensive set of tools that allow you to manage all aspects of OBS Studio operation on behalf of the user.\n\n## Capabilities\n\nYou can interact with OBS Studio to:\n- Control streaming, recording, and virtual camera functions\n- Manage scenes, sources, and scene items\n- Adjust audio settings and input properties\n- Handle transitions and studio mode\n- Capture screenshots and manage media\n- Configure OBS settings and profiles\n- Trigger hotkeys and special events\n\n## Communication Guidelines\n\n1. Be concise and precise in your responses. Users need clear instructions when dealing with streaming software.\n2. When a user asks for help with OBS, first determine if they have the OBS WebSocket server running and properly configured.\n3. If a user seems unfamiliar with OBS, offer brief explanations of key concepts as needed.\n4. When executing commands that change the state of OBS, always confirm success or report failures.\n5. Proactively suggest related actions that might be useful in the context of the user's request.\n6. Use technical terminology accurately but explain it when needed for less experienced users.\n7. When learning a user's workflow, always use appropriate tool calls to gather information about their current OBS setup (scenes, sources, etc.) before making recommendations.\n8. Always parse and explain tool responses in user-friendly language rather than displaying raw JSON.\n9. When suggesting complex operations that involve multiple steps, break them down clearly and execute them in a logical sequence.\n\n## Tool Usage\n\nYou have access to the following tools, organized by category:\n\n### General Operations\n- `obs-get-version`: Get OBS Studio and WebSocket protocol version information\n- `obs-get-stats`: Get system performance statistics (CPU, memory, disk space, etc.)\n- `obs-broadcast-custom-event`: Send a custom event to all WebSocket clients\n- `obs-call-vendor-request`: Call a request registered by a plugin\n- `obs-get-hotkey-list`: Get all available hotkey names\n- `obs-trigger-hotkey-by-name`: Trigger a hotkey by name\n- `obs-trigger-hotkey-by-key-sequence`: Trigger a hotkey using key combinations\n- `obs-sleep`: Delay execution for a specified number of milliseconds\n- `obs-get-persistent-data`: Get persistent data\n- `obs-set-persistent-data`: Set persistent data\n\n### Scene Management\n- `obs-get-scene-list`: Get a list of all scenes\n- `obs-get-current-scene`: Get the current program scene\n- `obs-set-current-scene`: Set the current program scene\n- `obs-get-preview-scene`: Get the current preview scene (Studio Mode)\n- `obs-set-preview-scene`: Set the current preview scene (Studio Mode)\n- `obs-create-scene`: Create a new scene\n- `obs-remove-scene`: Delete a scene\n- `obs-get-scene-collection-list`: Get a list of all scene collections\n- `obs-set-current-scene-collection`: Switch to a specific scene collection\n- `obs-create-scene-collection`: Create a new scene collection\n\n### Source and Input Control\n- `obs-get-source-active`: Check if a source is active\n- `obs-get-source-screenshot`: Get a screenshot of a source\n- `obs-save-source-screenshot`: Save a screenshot of a source to disk\n- `obs-get-input-list`: Get a list of all inputs\n- `obs-get-input-kind-list`: Get a list of available input kinds\n- `obs-get-special-inputs`: Get names of special inputs (audio, desktop, mic)\n- `obs-create-input`: Create a new input\n- `obs-remove-input`: Remove an input\n- `obs-set-input-name`: Set the name of an input\n- `obs-get-input-default-settings`: Get the default settings for an input kind\n- `obs-get-input-settings`: Get the settings of an input\n- `obs-set-input-settings`: Set the settings of an input\n- `obs-open-input-properties`: Open the properties dialog for an input\n- `obs-open-input-filters`: Open the filters dialog for an input\n- `obs-open-input-interact`: Open the interact dialog for an input\n\n### Audio Control\n- `obs-get-input-mute`: Get the mute status of an input\n- `obs-set-input-mute`: Set the mute status of an input\n- `obs-toggle-input-mute`: Toggle the mute status of an input\n- `obs-get-input-volume`: Get the volume of an input\n- `obs-set-input-volume`: Set the volume of an input\n- `obs-get-input-audio-balance`: Get the audio balance of an input\n- `obs-set-input-audio-balance`: Set the audio balance of an input\n- `obs-get-input-audio-sync-offset`: Get the audio sync offset of an input\n- `obs-set-input-audio-sync-offset`: Set the audio sync offset of an input\n- `obs-get-input-audio-monitor-type`: Get the audio monitoring type of an input\n- `obs-set-input-audio-monitor-type`: Set the audio monitoring type of an input\n\n### Scene Item Manipulation\n- `obs-get-scene-items`: Get a list of all scene items in a scene\n- `obs-create-scene-item`: Create a new scene item (add source to scene)\n- `obs-remove-scene-item`: Remove a scene item from a scene\n- `obs-set-scene-item-enabled`: Set the enabled status of a scene item\n- `obs-get-scene-item-transform`: Get the transform/position info of a scene item\n- `obs-set-scene-item-transform`: Set the transform/position info of a scene item\n- `obs-get-scene-item-id`: Get the ID of a scene item from its name\n\n### Streaming and Recording\n- `obs-get-stream-status`: Get the status of the stream output\n- `obs-start-stream`: Start streaming\n- `obs-stop-stream`: Stop streaming\n- `obs-toggle-stream`: Toggle streaming (start/stop)\n- `obs-send-stream-caption`: Send CEA-608 captioning data\n- `obs-get-record-status`: Get the status of the record output\n- `obs-toggle-record`: Toggle recording (start/stop)\n- `obs-start-record`: Start recording\n- `obs-stop-record`: Stop recording\n- `obs-toggle-record-pause`: Toggle record pause\n- `obs-pause-record`: Pause the record output\n- `obs-resume-record`: Resume the record output\n- `obs-split-record-file`: Split the current recording file\n- `obs-create-record-chapter`: Create a recording chapter\n- `obs-get-record-directory`: Get the directory that OBS is recording to\n- `obs-set-record-directory`: Set the directory that OBS will record to\n\n### Virtual Camera and Replay Buffer\n- `obs-get-virtual-cam-status`: Get the status of the virtual camera\n- `obs-toggle-virtual-cam`: Toggle virtual camera (start/stop)\n- `obs-start-virtual-cam`: Start virtual camera\n- `obs-stop-virtual-cam`: Stop virtual camera\n- `obs-get-replay-buffer-status`: Get the status of the replay buffer\n- `obs-toggle-replay-buffer`: Toggle the replay buffer (start/stop)\n- `obs-start-replay-buffer`: Start the replay buffer\n- `obs-stop-replay-buffer`: Stop the replay buffer\n- `obs-save-replay-buffer`: Save the contents of the replay buffer\n- `obs-get-last-replay-buffer-replay`: Get the filename of the last replay buffer save\n\n### Transitions\n- `obs-get-transition-list`: Get a list of all transitions\n- `obs-get-current-transition`: Get the current scene transition\n- `obs-set-current-transition`: Set the current scene transition\n- `obs-get-transition-duration`: Get the duration of the current transition\n- `obs-set-transition-duration`: Set the duration of the current transition\n- `obs-get-transition-kind`: Get the kind of a transition\n- `obs-get-transition-settings`: Get the settings of a transition\n- `obs-set-transition-settings`: Set the settings of a transition\n- `obs-trigger-transition`: Trigger a studio mode transition\n- `obs-trigger-studio-transition`: Same as trigger-transition\n\n### Profile Management\n- `obs-get-profile-list`: Get a list of all profiles\n- `obs-set-current-profile`: Set the current profile\n- `obs-create-profile`: Create a new profile\n- `obs-remove-profile`: Remove a profile\n- `obs-get-profile-parameter`: Get a parameter from the current profile\n- `obs-set-profile-parameter`: Set a parameter in the current profile\n\n### Output and Settings\n- `obs-get-video-settings`: Get video settings (base and output resolution, FPS)\n- `obs-set-video-settings`: Set video settings\n- `obs-get-stream-service-settings`: Get stream service settings\n- `obs-set-stream-service-settings`: Set stream service settings\n- `obs-get-output-list`: Get a list of all outputs\n- `obs-get-output-status`: Get the status of an output\n- `obs-toggle-output`: Toggle an output (on/off)\n- `obs-start-output`: Start an output\n- `obs-stop-output`: Stop an output\n- `obs-get-output-settings`: Get the settings of an output\n- `obs-set-output-settings`: Set the settings of an output\n\n### Studio Mode\n- `obs-get-studio-mode`: Get the status of studio mode\n- `obs-set-studio-mode`: Set the status of studio mode\n\n### Filters\n- `obs-get-filter-kind-list`: Get a list of all filter kinds\n- `obs-get-source-filter-list`: Get a list of all filters on a source\n- `obs-get-filter-default-settings`: Get the default settings for a filter kind\n- `obs-create-source-filter`: Create a new filter for a source\n- `obs-remove-source-filter`: Remove a filter from a source\n- `obs-set-source-filter-name`: Set the name of a source filter\n- `obs-get-source-filter`: Get settings of a source filter\n- `obs-set-source-filter-index`: Set the index position of a filter on a source\n- `obs-set-source-filter-settings`: Set the settings of a source filter\n- `obs-set-source-filter-enabled`: Set the enabled status of a source filter\n\n### Media Control\n- `obs-get-media-input-status`: Get the status of a media input\n- `obs-set-media-input-cursor`: Set the cursor position of a media input\n- `obs-offset-media-input-cursor`: Offset the cursor position of a media input\n- `obs-trigger-media-input-action`: Trigger an action on a media input\n\n### Projectors\n- `obs-get-monitor-list`: Get a list of all monitors/displays\n- `obs-open-video-mix-projector`: Open a projector for a specific monitor\n- `obs-open-source-projector`: Open a projector for a source\n\n## Troubleshooting\n\nIf commands fail, check for these common issues:\n1. OBS WebSocket connection issues (verify OBS is running and WebSocket server is enabled)\n2. Password authentication problems\n3. Resource names that don't match (case-sensitive)\n4. Insufficient permissions or conflicting operations\n\n## Response Format\n\nWhen executing commands:\n1. Acknowledge the user's request\n2. Execute the relevant OBS command\n3. Interpret the results for the user\n4. Suggest next steps or related actions\n\n## Examples\n\nFor \"Start my stream\":\n```\nI'll start your OBS stream now.\n[Execute obs-start-stream]\nYour stream has been started successfully. Would you like me to also start recording as a backup?\n```\n\nFor \"Switch to my gaming scene\":\n```\nI'll switch to your gaming scene.\n[Execute obs-get-scene-list to verify the scene exists]\n[Execute obs-set-current-scene with \"Gaming\" as parameter]\nSuccessfully switched to your Gaming scene. Your gameplay is now visible to viewers.\n```\n\nFor \"Help me set up a new scene\":\n```\nI'd be happy to help you create a new scene in OBS. What would you like to name it?\n[After user provides name]\n[Execute obs-create-scene with the provided name]\nGreat! I've created a new scene called \"[name]\". Would you like to add some sources to it now?\n```\n\nAlways prioritize helping the user accomplish their streaming goals efficiently and effectively. Focus on making OBS operation as smooth and user-friendly as possible."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [7292, 260], "id": "d3ae5688-4dc4-4d92-abd7-36bb5fb42c23", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-120, 480], "id": "c8c61a66-fe88-484b-85be-d369397cd9b9", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "avSgLKwLvvI1EPHE", "name": "OpenAi account"}}}, {"parameters": {"connectionType": "sse"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [0, 480], "id": "84ebde6d-ece7-49e8-a036-b1f43160ff43", "name": "MCP Client", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-version"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [120, 480], "id": "afdc344d-dd3d-455a-8837-c10916dbc76d", "name": "obs-get-version", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-stats"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [240, 480], "id": "e4e88691-926d-4bc9-bead-133c26ddf4dc", "name": "obs-get-stats", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-broadcast-custom-event"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [360, 480], "id": "ef964349-7a0c-4be0-b378-f71c9144c407", "name": "obs-broadcast-custom-event", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-call-vendor-request"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [480, 480], "id": "e9cabdd8-5b3b-4e55-9ed7-78dde9927fd2", "name": "obs-call-vendor-request", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-hotkey-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [600, 480], "id": "23fbf0db-0bb7-4895-b6a8-8b63b768ef35", "name": "obs-get-hotkey-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-trigger-hotkey-by-name"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [720, 480], "id": "c1998822-7e3e-41b6-b280-bdb78751ad57", "name": "obs-trigger-hotkey-by-name", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-trigger-hotkey-by-key-sequence"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [840, 480], "id": "00175de8-fb5c-4dba-9863-a804196ce92a", "name": "obs-trigger-hotkey-by-key-sequence", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-sleep"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [960, 480], "id": "9cc4bbf9-7397-4b85-8dc5-7c72154e385b", "name": "obs-sleep", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-scene-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1080, 480], "id": "dfd88330-426a-4e25-b2db-83786c6e6a9b", "name": "obs-get-scene-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-current-scene"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1200, 480], "id": "dae72d34-ecff-4c0f-af07-c211c5bf8832", "name": "obs-get-current-scene", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-preview-scene"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1320, 480], "id": "611fdaf7-ecfc-424a-aea7-7a87c4abbdf0", "name": "obs-get-preview-scene", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-preview-scene"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1440, 480], "id": "13892f94-7c0d-46be-9252-7b2735e12ca4", "name": "obs-set-preview-scene", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-create-scene"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1560, 480], "id": "26643347-1787-44f2-ae91-bca04cb07282", "name": "obs-create-scene", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-remove-scene"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1680, 480], "id": "c0bc2483-5d24-49b3-b53e-f4b6be4b6246", "name": "obs-remove-scene", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-trigger-studio-transition"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1800, 480], "id": "4e0e1d23-d35d-4f4f-a95e-c1e3deafb02f", "name": "obs-trigger-studio-transition", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-source-active"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [1920, 480], "id": "66a3b3fc-07b0-4be7-980f-c71ea23bcb68", "name": "obs-get-source-active", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-source-screenshot"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2040, 480], "id": "b83533ee-7c74-488b-bade-8c314c5260bf", "name": "obs-get-source-screenshot", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-save-source-screenshot"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2160, 480], "id": "d366c141-6084-44b8-822a-191403e020a0", "name": "obs-save-source-screenshot", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-scene-items"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2280, 480], "id": "2249b632-c605-4bc4-900f-b3aa0a8601e5", "name": "obs-get-scene-items", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-create-scene-item"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2400, 480], "id": "e9ad55bd-73eb-4e0b-abf2-1be71aa32233", "name": "obs-create-scene-item", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-remove-scene-item"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2520, 480], "id": "82447d5f-e03d-442d-b7bb-2bff82bf63b4", "name": "obs-remove-scene-item", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-scene-item-enabled"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2640, 480], "id": "7cc69675-abc0-480a-b06f-e9f14668dd1d", "name": "obs-set-scene-item-enabled", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-scene-item-transform"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2760, 480], "id": "44cce653-2264-4044-81d1-c3f1f29bd695", "name": "obs-get-scene-item-transform", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-scene-item-transform"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2880, 480], "id": "0ad24a7c-c334-45c7-8f67-61478a65d731", "name": "obs-set-scene-item-transform", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-scene-item-id"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3000, 480], "id": "cad39363-c027-47ca-85f5-2e1e41d31278", "name": "obs-get-scene-item-id", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-stream-status"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3120, 480], "id": "cadfc668-91db-4070-8f07-b36524e410b3", "name": "obs-get-stream-status", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-start-stream"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3240, 480], "id": "ae04e1ab-e47f-4ad7-a28d-52ca0c639ea8", "name": "obs-start-stream", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-stop-stream"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3360, 480], "id": "43cab016-bf9c-44b5-9910-5796525d88d0", "name": "obs-stop-stream", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-toggle-stream"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3480, 480], "id": "43c6dc54-d4b6-4c88-965b-d1722eaf593b", "name": "obs-toggle-stream", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-send-stream-caption"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3600, 480], "id": "107d915b-1ab3-48f7-82be-5d8aefc67993", "name": "obs-send-stream-caption", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-transition-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3720, 480], "id": "595907d5-66ab-40d7-bcb4-3b612fbb056d", "name": "obs-get-transition-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-current-transition"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3840, 480], "id": "facb63e0-9d1d-4554-8789-3c2388c08d07", "name": "obs-get-current-transition", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-current-transition"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3960, 480], "id": "9fee8da5-e2ea-4021-8c79-90d398ad10c6", "name": "obs-set-current-transition", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-transition-duration"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [4080, 480], "id": "fbf5a7a4-047f-4231-bc94-3a5ff3617e81", "name": "obs-get-transition-duration", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-transition-duration"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [4200, 480], "id": "18de7e00-a3d4-4462-b88f-329be75805fb", "name": "obs-set-transition-duration", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-transition-kind"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [4320, 480], "id": "3e31e73b-f0d9-473e-b38b-f569b6696ea6", "name": "obs-get-transition-kind", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-transition-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [4440, 480], "id": "4e7b0c3c-9be3-4f31-b4e7-e2ddda5e6195", "name": "obs-set-transition-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-transition-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [4560, 480], "id": "6dbe9da9-d2e8-45b5-8dab-8950171caa2f", "name": "obs-get-transition-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-trigger-transition"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [4680, 480], "id": "b3438c60-82b2-493f-8229-8d3b5ed91d53", "name": "obs-trigger-transition", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-persistent-data"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [4800, 480], "id": "53293980-a64f-41da-9b15-652ea1500e58", "name": "obs-get-persistent-data", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-persistent-data"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [4920, 480], "id": "177acfe8-b945-4ce7-94d1-42aea408e69b", "name": "obs-set-persistent-data", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-scene-collection-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [5040, 480], "id": "15a15950-59ec-4285-bc2a-41477d2e9772", "name": "obs-get-scene-collection-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-current-scene-collection"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [5160, 480], "id": "9c7a51ac-fe3b-43b6-9312-36e42f0d8d2c", "name": "obs-set-current-scene-collection", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-create-scene-collection"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [5280, 480], "id": "ee75d46d-893c-4a9e-965a-8389a4bf6892", "name": "obs-create-scene-collection", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-profile-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [5400, 480], "id": "fcf81fbf-70af-41ab-bd8c-b6a3cafba374", "name": "obs-get-profile-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-current-profile"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [5520, 480], "id": "355d9e1b-8f42-4f5b-90da-154a82626ea2", "name": "obs-set-current-profile", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-create-profile"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [5640, 480], "id": "55e5bf1d-b171-4dab-a89c-3da95e2ad11c", "name": "obs-create-profile", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-remove-profile"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [5760, 480], "id": "1a56eb9b-699d-4729-97ad-c326a333d35b", "name": "obs-remove-profile", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-profile-parameter"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [5880, 480], "id": "ee2ba9ea-6ebd-48b1-a3d0-636dcd8652a9", "name": "obs-get-profile-parameter", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-profile-parameter"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6000, 480], "id": "d215a37b-518a-42ba-90c9-0169767920a8", "name": "obs-set-profile-parameter", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-video-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6120, 480], "id": "7e252c66-af6d-431e-8691-5b81b45e545e", "name": "obs-get-video-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-video-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6240, 480], "id": "560fb1cc-54c6-48ad-9623-852c2ea410d6", "name": "obs-set-video-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-stream-service-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6360, 480], "id": "366579a9-d2a7-4d76-bf31-a0fc9bfd7be4", "name": "obs-get-stream-service-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-stream-service-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6480, 480], "id": "71e4852a-338f-4621-a98d-bf1b05c0d0ce", "name": "obs-set-stream-service-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-record-directory"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6600, 480], "id": "c59082df-93dd-4454-b9fd-c3f92e543ea2", "name": "obs-get-record-directory", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-record-directory"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6720, 480], "id": "dd5a2296-b8da-4e95-8a4d-a140d84a6d64", "name": "obs-set-record-directory", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-filter-kind-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6840, 480], "id": "a219cfae-0663-40cb-bc59-6d6478f5ab74", "name": "obs-get-filter-kind-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-source-filter-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [6960, 480], "id": "61963a4d-9371-487b-b5b8-8157746c29eb", "name": "obs-get-source-filter-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-filter-default-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [7080, 480], "id": "db589a2b-0abd-4903-86f0-6ef4e8d41260", "name": "obs-get-filter-default-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-create-source-filter"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [7200, 480], "id": "61f0ef30-8a1c-4a70-b8cd-a28be2c8a119", "name": "obs-create-source-filter", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-remove-source-filter"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [7320, 480], "id": "0f9f7a39-c29d-4827-a6ac-054dcbb0d481", "name": "obs-remove-source-filter", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-source-filter-name"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [7440, 480], "id": "6354a604-06b9-4c0d-9548-6d3a85eafff6", "name": "obs-set-source-filter-name", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-source-filter"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [7560, 480], "id": "cc3fd10f-c453-4aa5-b186-c9ab88874677", "name": "obs-get-source-filter", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-source-filter-index"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [7680, 480], "id": "96a7a455-1500-4397-b893-e144df054c82", "name": "obs-set-source-filter-index", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-source-filter-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [7800, 480], "id": "554c0b55-3eeb-4f9d-bb8d-b495dcb1ca06", "name": "obs-set-source-filter-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-source-filter-enabled"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [7920, 480], "id": "29752353-8069-44a2-9a93-18c9dc3abbd0", "name": "obs-set-source-filter-enabled", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [8040, 480], "id": "3c68c5b3-4cdc-42d9-a327-a6ece1ac503c", "name": "obs-get-input-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-kind-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [8160, 480], "id": "fc4c62a6-1288-4149-ba1d-0b4316dd61d1", "name": "obs-get-input-kind-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-special-inputs"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [8280, 480], "id": "f6d774d7-de39-4d64-b33e-6d068f8aba48", "name": "obs-get-special-inputs", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-create-input"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [8400, 480], "id": "cefba6a2-0d82-4d1b-bfad-c3211161d51c", "name": "obs-create-input", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-remove-input"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [8520, 480], "id": "c0b2c5e6-6070-4a9b-8cb6-fa11a7b3695a", "name": "obs-remove-input", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-input-name"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [8640, 480], "id": "fc2de439-a288-410e-84cf-670fb9895404", "name": "obs-set-input-name", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-default-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [8760, 480], "id": "5147b8fe-d432-4535-b87c-15a7636e1a4e", "name": "obs-get-input-default-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [8880, 480], "id": "f6b54f6d-9f1e-4798-83af-ce455a118ae8", "name": "obs-get-input-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-input-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9000, 480], "id": "8b5fc070-528a-4503-84da-a783b00e5925", "name": "obs-set-input-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-mute"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9120, 480], "id": "18c78516-f81b-4e55-9a3d-599610d31cfd", "name": "obs-get-input-mute", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-input-mute"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9240, 480], "id": "c0c54f8c-4f15-4ec4-9444-5c13b556aa1d", "name": "obs-set-input-mute", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-toggle-input-mute"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9360, 480], "id": "a3d495d7-c347-4d46-aa4b-813e53e596ff", "name": "obs-toggle-input-mute", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-volume"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9480, 480], "id": "ba0a71f2-7840-4a89-bbe1-3a9df9145dc8", "name": "obs-get-input-volume", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-input-volume"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9600, 480], "id": "4889dee1-73e5-4907-9427-701a52710cd6", "name": "obs-set-input-volume", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-audio-balance"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9720, 480], "id": "3d4aaf2d-9278-45c1-a8dd-d00015b94295", "name": "obs-get-input-audio-balance", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-input-audio-balance"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9840, 480], "id": "bbec2303-7ebf-440a-b36f-e7e39166ed79", "name": "obs-set-input-audio-balance", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-audio-sync-offset"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [9960, 480], "id": "9fa44eac-b958-400b-bd77-00a93541385f", "name": "obs-get-input-audio-sync-offset", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-input-audio-sync-offset"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [10080, 480], "id": "f782ed3e-16ab-4e70-966a-333136eaf388", "name": "obs-set-input-audio-sync-offset", "credentials": {"mcpClientSseApi": {"id": "uuVg1tmbqR4kUs3f", "name": "MCP Client - triliumnext-mcp"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-input-audio-monitor-type"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [10200, 480], "id": "3038645a-6cc1-4298-b1a5-496acfbbd1f6", "name": "obs-get-input-audio-monitor-type", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-input-audio-monitor-type"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [10320, 480], "id": "11bfe4f8-914c-4183-a299-5d73aba416df", "name": "obs-set-input-audio-monitor-type", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-media-input-status"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [10440, 480], "id": "aa85479e-26a4-42db-a442-b908c252e740", "name": "obs-get-media-input-status", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-media-input-cursor"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [10560, 480], "id": "10dd1c54-837d-4e0d-af8a-c2798e032357", "name": "obs-set-media-input-cursor", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-offset-media-input-cursor"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [10680, 480], "id": "66edf2fa-0df0-41f8-aabd-672546b01ff1", "name": "obs-offset-media-input-cursor", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-trigger-media-input-action"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [10800, 480], "id": "2da71fe3-d345-4f97-acbc-8680b2174a87", "name": "obs-trigger-media-input-action", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-virtual-cam-status"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [10920, 480], "id": "8488f7aa-a9c6-4b9b-a09b-942ea0582a39", "name": "obs-get-virtual-cam-status", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-toggle-virtual-cam"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [11040, 480], "id": "dbe7334e-3e20-433d-8d38-2df0f5a9ada7", "name": "obs-toggle-virtual-cam", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-start-virtual-cam"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [11160, 480], "id": "f65d3018-bfd8-4d23-8848-f38b815fd937", "name": "obs-start-virtual-cam", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-stop-virtual-cam"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [11280, 480], "id": "a506e6cb-091a-4cfa-8298-26d43f256f32", "name": "obs-stop-virtual-cam", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-replay-buffer-status"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [11400, 480], "id": "ce568d5f-90cb-4683-bcb7-b678717db90d", "name": "obs-get-replay-buffer-status", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-toggle-replay-buffer"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [11520, 480], "id": "c6a98a04-1eb2-40e3-b58b-235b3bfdf604", "name": "obs-toggle-replay-buffer", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-start-replay-buffer"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [11640, 480], "id": "675b2763-24fa-4fc2-b04f-c74ff4815f3d", "name": "obs-start-replay-buffer", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-stop-replay-buffer"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [11760, 480], "id": "ebb228aa-f2d6-4e0b-9b80-ee71978d1c3f", "name": "obs-stop-replay-buffer", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-save-replay-buffer"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [11880, 480], "id": "48d76c7b-94e5-4469-b99d-de1a096d9f54", "name": "obs-save-replay-buffer", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-last-replay-buffer-replay"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12000, 480], "id": "c691d0ca-07a9-44cb-8664-dfbdff772107", "name": "obs-get-last-replay-buffer-replay", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-output-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12120, 480], "id": "24b6d939-07e1-4d74-88d4-fa30055d551a", "name": "obs-get-output-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-output-status"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12240, 480], "id": "728e3df1-fe92-417b-900b-1311cf9013df", "name": "obs-get-output-status", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-toggle-output"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12360, 480], "id": "4b80f183-c1a1-4717-a937-80b0dcd14834", "name": "obs-toggle-output", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-start-output"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12480, 480], "id": "a5e74a0c-4fb7-4c31-99bc-6962c58882e0", "name": "obs-start-output", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-stop-output"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12600, 480], "id": "12abef51-4797-4cbb-8e20-7f14f7f9307e", "name": "obs-stop-output", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-output-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12720, 480], "id": "f7628af2-4000-465c-955f-4f4354046dc5", "name": "obs-get-output-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-output-settings"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12840, 480], "id": "06fe2d6d-7257-4ecf-93ef-2e7727da5d48", "name": "obs-set-output-settings", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-record-status"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [12960, 480], "id": "8724dcd4-84bf-4258-82f1-376500b65825", "name": "obs-get-record-status", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-toggle-record"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [13080, 480], "id": "b288eb21-0fd8-483d-b90b-da6562e38e6d", "name": "obs-toggle-record", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-start-record"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [13200, 480], "id": "c23b9c62-00e2-4b84-a123-0e13787b43a0", "name": "obs-start-record", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-stop-record"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [13320, 480], "id": "df599b52-1f0c-4f03-9255-b2f434e1702a", "name": "obs-stop-record", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-toggle-record-pause"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [13440, 480], "id": "dd601930-5a14-4e0f-9c96-219a37205c3a", "name": "obs-toggle-record-pause", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-pause-record"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [13560, 480], "id": "5b75cc9c-e297-48c5-ac62-627e94a263e3", "name": "obs-pause-record", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-resume-record"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [13680, 480], "id": "37f9ef2e-a02a-406b-b851-02f07fb42724", "name": "obs-resume-record", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-split-record-file"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [13800, 480], "id": "52ad038c-848b-44a9-8b00-70d03e5f0b0b", "name": "obs-split-record-file", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-create-record-chapter"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [13920, 480], "id": "f8d1756e-82f1-4e06-a352-34502c4cb8a7", "name": "obs-create-record-chapter", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-studio-mode"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [14040, 480], "id": "a0f20f6b-e507-4e75-8e7d-9178d28ae0ac", "name": "obs-get-studio-mode", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-set-studio-mode"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [14160, 480], "id": "24777d84-41a1-4ea2-af47-f748b71da494", "name": "obs-set-studio-mode", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-open-input-properties"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [14280, 480], "id": "161c46ab-2f78-4b55-9696-5882b28767c4", "name": "obs-open-input-properties", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-open-input-filters"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [14400, 480], "id": "30da926b-bb94-423d-a7cd-e522eb093e13", "name": "obs-open-input-filters", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-open-input-interact"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [14520, 480], "id": "ce867242-6266-4014-b194-72110d7d2957", "name": "obs-open-input-interact", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-get-monitor-list"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [14640, 480], "id": "b7fc92cd-f81b-4bf7-8386-869ba42d8838", "name": "obs-get-monitor-list", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-open-video-mix-projector"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [14760, 480], "id": "19619209-240c-49b4-825a-68e3ecf48506", "name": "obs-open-video-mix-projector", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "obs-open-source-projector"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [14880, 480], "id": "afdc45ce-d308-4238-99ef-c8edc5194eb8", "name": "obs-open-source-projector", "credentials": {"mcpClientSseApi": {"id": "z58ktCyPpFSZTi98", "name": "MCP Client - OBS"}}}, {"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"input\": \"set up a new scene for my webcam and desktop\",\n  \"reason\": \"The request involves creating and configuring scenes in OBS Studio, which is a primary capability of the OBS Agent.\",\n  \"selectedAgent\": \"obs-agent\"\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [7040, 220], "id": "aaa78765-5bcc-4ea1-9b6f-9e6cd7e96994", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"When chat message received": {"main": [[]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-version": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-stats": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-broadcast-custom-event": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-call-vendor-request": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-hotkey-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-trigger-hotkey-by-name": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-trigger-hotkey-by-key-sequence": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-sleep": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-scene-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-current-scene": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-preview-scene": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-preview-scene": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-create-scene": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-remove-scene": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-trigger-studio-transition": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-source-active": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-source-screenshot": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-save-source-screenshot": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-scene-items": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-create-scene-item": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-remove-scene-item": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-scene-item-enabled": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-scene-item-transform": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-scene-item-transform": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-scene-item-id": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-stream-status": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-start-stream": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-stop-stream": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-toggle-stream": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-send-stream-caption": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-transition-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-current-transition": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-current-transition": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-transition-duration": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-transition-duration": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-transition-kind": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-transition-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-transition-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-trigger-transition": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-persistent-data": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-persistent-data": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-scene-collection-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-current-scene-collection": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-create-scene-collection": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-profile-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-current-profile": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-create-profile": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-remove-profile": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-profile-parameter": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-profile-parameter": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-video-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-video-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-stream-service-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-stream-service-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-record-directory": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-record-directory": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-filter-kind-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-source-filter-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-filter-default-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-create-source-filter": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-remove-source-filter": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-source-filter-name": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-source-filter": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-source-filter-index": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-source-filter-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-source-filter-enabled": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-kind-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-special-inputs": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-create-input": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-remove-input": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-input-name": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-default-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-input-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-mute": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-input-mute": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-toggle-input-mute": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-volume": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-input-volume": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-audio-balance": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-input-audio-balance": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-audio-sync-offset": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-input-audio-sync-offset": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-input-audio-monitor-type": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-input-audio-monitor-type": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-media-input-status": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-media-input-cursor": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-offset-media-input-cursor": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-trigger-media-input-action": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-virtual-cam-status": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-toggle-virtual-cam": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-start-virtual-cam": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-stop-virtual-cam": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-replay-buffer-status": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-toggle-replay-buffer": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-start-replay-buffer": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-stop-replay-buffer": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-save-replay-buffer": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-last-replay-buffer-replay": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-output-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-output-status": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-toggle-output": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-start-output": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-stop-output": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-output-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-output-settings": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-record-status": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-toggle-record": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-start-record": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-stop-record": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-toggle-record-pause": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-pause-record": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-resume-record": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-split-record-file": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-create-record-chapter": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-studio-mode": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-set-studio-mode": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-open-input-properties": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-open-input-filters": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-open-input-interact": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-get-monitor-list": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-open-video-mix-projector": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "obs-open-source-projector": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "4bc7e458-dd16-482c-a448-06c5b31b1690", "meta": {"templateCredsSetupCompleted": true, "instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "id": "Z3gZAj9TvyJ3TCEj", "tags": []}