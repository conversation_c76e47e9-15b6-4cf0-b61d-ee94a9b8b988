# Project NOVA Environment Configuration
# Copy this file to .env and update the values according to your setup

# General Configuration
HOST_IP=0.0.0.0

# n8n Configuration
N8N_USER=admin
N8N_PASSWORD=password
N8N_HOST=localhost
TIMEZONE=UTC

# Local Agent Ports (for local setup)
BLINKO_PORT=3001
BOOKSTACK_PORT=3003
TRILIUM_PORT=3005
SIYUAN_PORT=3006
PAPERLESS_PORT=3008
KARAKEEP_PORT=3009

# Blinko Agent - Note-taking and knowledge management
BLINKO_DOMAIN=http://localhost:3001
BLINKO_API_KEY=your-blinko-api-key

# BookStack Agent - Documentation management
BOOKSTACK_API_URL=http://localhost
BOOKSTACK_API_TOKEN=your-bookstack-token

# Home Assistant Agent - Smart home automation
HA_URL=http://localhost:8123
HA_TOKEN=your-home-assistant-token

# OBS Agent - Streaming and recording
OBS_HOST=localhost
OBS_PORT=4455
OBS_PASSWORD=your-obs-password

# Reaper Agent - Digital Audio Workstation
REAPER_HOST=localhost
REAPER_SEND_PORT=8000
REAPER_RECEIVE_PORT=9000
DEBUG_MODE=false

# Langfuse Agent - LLM observability
LANGFUSE_SECRET_KEY=your-langfuse-secret-key
LANGFUSE_PUBLIC_KEY=your-langfuse-public-key
LANGFUSE_HOST=https://cloud.langfuse.com

# Outline Agent - Team wiki
OUTLINE_API_KEY=your-outline-api-key
OUTLINE_API_URL=https://your-outline.com

# Puppeteer Agent - Web automation
ALLOW_DANGEROUS=false

# Karakeep Agent - Task management
KARAKEEP_API_ADDR=http://localhost:8080
KARAKEEP_API_KEY=your-karakeep-api-key

# SiYuan Agent - Note-taking
SIYUAN_TOKEN=your-siyuan-token
SIYUAN_SERVER=http://localhost:6806

# Paperless Agent - Document management
PAPERLESS_URL=http://localhost:8000
PAPERLESS_TOKEN=your-paperless-token

# Memos Agent - Micro-blogging
MEMOS_URL=http://localhost:5230
MEMOS_TOKEN=your-memos-token

# Flowise Agent - AI workflow
FLOWISE_API_KEY=your-flowise-api-key
FLOWISE_API_ENDPOINT=http://localhost:3000

# Gitea/Forgejo Agent - Git repository management
GITEA_HOST=https://gitea.com
GITEA_ACCESS_TOKEN=your-gitea-token

# OnlyOffice Agent - Document editing
ONLYOFFICE_URL=http://localhost:8080
ONLYOFFICE_TOKEN=your-onlyoffice-token

# Prometheus Agent - Monitoring
PROMETHEUS_URL=http://localhost:9090

# RAGFlow Agent - RAG system
RAGFLOW_API_KEY=your-ragflow-api-key
RAGFLOW_BASE_URL=http://localhost:9380

# TriliumNext Agent - Hierarchical note-taking
TRILIUM_URL=http://localhost:8080
TRILIUM_TOKEN=your-trilium-token

# YouTube Agent - Video management
YOUTUBE_API_KEY=your-youtube-api-key

# System Search Agent - Local system search
SEARCH_INDEX_PATH=/data/search-index

# Ableton Copilot Agent - Music production
ABLETON_IP=localhost
BASE_PATH=/data/ableton
