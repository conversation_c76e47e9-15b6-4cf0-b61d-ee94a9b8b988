# Project NOVA 🚀

![Project NOVA](https://img.shields.io/badge/Project%20NOVA-v1.0-blue.svg) ![GitHub Release](https://img.shields.io/github/release/PradeepaRW/project-nova.svg)

Welcome to **Project NOVA**! This repository hosts a multi-agent AI architecture designed to connect over 25 specialized agents through n8n and MCP servers. Our goal is to route requests to domain-specific experts, allowing for seamless control of various applications, from knowledge bases to digital audio workstations (DAWs), home automation systems, and development tools.

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Architecture](#architecture)
4. [Getting Started](#getting-started)
5. [Usage](#usage)
6. [Workflows](#workflows)
7. [Contributing](#contributing)
8. [License](#license)
9. [Contact](#contact)
10. [Releases](#releases)

## Overview

**Project NOVA** integrates multiple AI agents to enhance your workflow. By connecting various tools and systems, it streamlines tasks and improves efficiency. Each agent specializes in a specific domain, ensuring that you receive the best assistance for your needs.

## Features

- **Multi-Agent Architecture**: Connects 25+ specialized agents.
- **Flexible Integration**: Works with n8n and MCP servers for smooth operation.
- **Domain-Specific Expertise**: Routes requests to the right agent based on your needs.
- **Comprehensive Ecosystem**: Includes system prompts, Dockerfiles, and workflows.
- **User-Friendly**: Designed for ease of use and quick setup.

## Architecture

The architecture of Project NOVA consists of several key components:

- **Agents**: Each agent specializes in a specific task or domain.
- **n8n Server**: Acts as the workflow automation tool, connecting agents and managing requests.
- **MCP Server**: Handles communication between agents and external applications.
- **Docker Containers**: Provides a consistent environment for running the agents.

![Architecture Diagram](https://via.placeholder.com/800x400.png?text=Architecture+Diagram)

## Getting Started

To get started with Project NOVA, follow these steps:

### Prerequisites

- **Docker**: Make sure you have Docker installed on your system
  - [Install Docker](https://docs.docker.com/get-docker/)
- **Docker Compose**: Required for orchestrating multiple services
  - [Install Docker Compose](https://docs.docker.com/compose/install/)

### Quick Setup (Recommended)

1. **Clone the Repository**:
   ```bash
   git clone https://github.com/PradeepaRW/project-nova.git
   cd project-nova
   ```

2. **Run the Setup Script**:
   ```bash
   # Make the setup script executable
   chmod +x setup.sh

   # Run full setup (checks prerequisites, builds images, starts services)
   ./setup.sh setup
   ```

3. **Configure Environment Variables**:
   - The setup script creates a `.env` file from `.env.example`
   - Edit `.env` with your actual API keys and service URLs:
   ```bash
   nano .env
   ```

4. **Access the Services**:
   - **n8n Dashboard**: http://localhost:5678 (Username: `admin`, Password: `password`)
   - **Router Agent**: http://localhost:3000
   - **Other Agents**: See [Service URLs](#service-urls) section below

### Manual Setup

If you prefer manual setup:

1. **Clone and Navigate**:
   ```bash
   git clone https://github.com/PradeepaRW/project-nova.git
   cd project-nova
   ```

2. **Setup Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Services**:
   ```bash
   docker-compose up -d
   ```

### Service URLs

Once running, the following services will be available:

| Service | URL | Description |
|---------|-----|-------------|
| n8n Dashboard | http://localhost:5678 | Main workflow automation interface |
| Router Agent | http://localhost:3000 | Central request routing |
| Blinko Agent | http://localhost:3001 | Note-taking and knowledge management |
| BookStack Agent | http://localhost:3003 | Documentation management |
| Home Assistant Agent | http://localhost:3004 | Smart home automation |
| Puppeteer Agent | http://localhost:3007 | Web automation |
| OBS Agent | http://localhost:3012 | Streaming and recording |
| Reaper Agent | http://localhost:3013 | Digital Audio Workstation |
| Langfuse Agent | http://localhost:3014 | LLM observability |
| Outline Agent | http://localhost:6060 | Team wiki |

## Usage

Using Project NOVA is straightforward. Once set up, you can interact with the agents through the n8n interface or directly via their APIs.

### Managing Services

Use the provided setup script for easy service management:

```bash
# Start all services
./setup.sh start

# Stop all services
./setup.sh stop

# Restart services
./setup.sh restart

# View service status
./setup.sh status

# View logs (Ctrl+C to exit)
./setup.sh logs

# Clean up (removes all containers and volumes)
./setup.sh clean
```

### Interacting with Agents

1. **Through n8n Dashboard**:
   - Access http://localhost:5678
   - Import workflows from the `n8n-workflows/` directory
   - Configure and trigger workflows that route requests to appropriate agents

2. **Direct API Access**:
   - Each agent exposes its own API endpoint
   - Use the agent-specific documentation for API details
   - Send requests directly to agent ports (see Service URLs table above)

3. **Agent Selection Process**:
   - **Router Agent** analyzes incoming requests
   - Routes to the most appropriate specialized agent
   - Returns consolidated responses

### Configuration

#### Environment Variables
Edit the `.env` file to configure:
- API keys for external services
- Service URLs and endpoints
- Agent-specific settings
- Authentication credentials

#### Adding New Agents
1. Create a new Dockerfile in `mcp-server-dockerfiles/your-agent-mcp/`
2. Add the service to `docker-compose.yml`
3. Create corresponding n8n workflow in `n8n-workflows/`
4. Update environment variables as needed

## Workflows

Project NOVA comes with predefined n8n workflows located in the `n8n-workflows/` directory. These workflows demonstrate how to integrate and orchestrate the various agents.

### Available Workflows

- **Router Agent**: Central request routing and agent selection
- **Blinko Agent**: Note-taking and knowledge management automation
- **BookStack Agent**: Documentation workflow automation
- **Home Assistant Agent**: Smart home device control and automation
- **Puppeteer Agent**: Web scraping and browser automation
- **OBS Agent**: Streaming and recording automation
- **Reaper Agent**: Digital Audio Workstation control
- **Langfuse Agent**: LLM observability and monitoring
- **And many more...**

### Importing Workflows

1. Access the n8n dashboard at http://localhost:5678
2. Go to **Workflows** → **Import from File**
3. Select workflow files from the `n8n-workflows/` directory
4. Configure the workflow with your specific settings
5. Activate the workflow

### Example Use Cases

1. **Smart Home Automation**:
   - Voice command → Router Agent → Home Assistant Agent → Device Control

2. **Content Creation Pipeline**:
   - Research request → Puppeteer Agent → Content gathering → Outline Agent → Documentation

3. **Music Production Workflow**:
   - Project request → Router Agent → Reaper Agent → DAW automation

4. **Knowledge Management**:
   - Query → Router Agent → Blinko/BookStack Agent → Information retrieval

### Customizing Workflows

- Modify existing workflows in n8n dashboard
- Create new workflows combining multiple agents
- Add custom logic and conditions
- Integrate with external APIs and services

## Troubleshooting

### Common Issues

1. **Port Conflicts**:
   - Check if ports are already in use: `netstat -tulpn | grep :5678`
   - Modify port mappings in `docker-compose.yml` if needed

2. **Service Won't Start**:
   - Check logs: `./setup.sh logs` or `docker-compose logs [service-name]`
   - Verify environment variables in `.env` file
   - Ensure Docker has sufficient resources allocated

3. **Agent Not Responding**:
   - Verify the agent's external service is running and accessible
   - Check API keys and authentication in `.env`
   - Review agent-specific documentation

4. **n8n Workflows Not Working**:
   - Ensure all required environment variables are set
   - Check workflow node configurations
   - Verify agent endpoints are accessible

### Getting Help

- Check the logs: `docker-compose logs -f`
- Review individual agent documentation in `agents/` directory
- Verify your `.env` configuration matches `.env.example`

## Contributing

We welcome contributions to Project NOVA! If you'd like to help improve the project, please follow these steps:

1. **Fork the Repository**: Click the fork button at the top right of the page.
2. **Create a New Branch**:
   ```bash
   git checkout -b feature/YourFeatureName
   ```
3. **Make Your Changes**: Implement your feature or fix.
4. **Test Your Changes**:
   ```bash
   ./setup.sh build
   ./setup.sh start
   ```
5. **Commit Your Changes**:
   ```bash
   git commit -m "Add your message here"
   ```
6. **Push to Your Fork**:
   ```bash
   git push origin feature/YourFeatureName
   ```
7. **Create a Pull Request**: Go to the original repository and create a pull request.

### Contributing Guidelines

- Follow existing code style and structure
- Update documentation for new features
- Test your changes with the provided setup script
- Add new agents following the existing pattern in `mcp-server-dockerfiles/`

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

## Contact

For any inquiries or support, please reach out to the project maintainers:

- **Email**: [<EMAIL>](mailto:<EMAIL>)
- **GitHub**: [PradeepaRW](https://github.com/PradeepaRW)

## Releases

To download the latest release of Project NOVA, visit our [Releases](https://github.com/PradeepaRW/project-nova/releases) section. Here, you can find the necessary files to download and execute for your setup.

You can also check the [Releases](https://github.com/PradeepaRW/project-nova/releases) section for updates and new features.

---

Thank you for exploring Project NOVA! We look forward to your contributions and feedback.